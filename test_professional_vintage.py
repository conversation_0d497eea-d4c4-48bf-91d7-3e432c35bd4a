#!/usr/bin/env python3
"""
Test the complete professional vintage video workflow:
1. Generate smooth karaoke video with enhanced readability
2. Apply vintage texture overlay (dust/scratches)
3. Compare with traditional captions
"""
import requests
import time

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_TEXT = "This is a professional vintage video with enhanced karaoke animation and authentic film texture."

# Free vintage texture overlay URLs (black background with white dust/scratches)
VINTAGE_TEXTURE_URLS = [
    "https://cdn.pixabay.com/video/2019/12/09/29767-380002046_large.mp4",  # Film grain overlay
    "https://cdn.pixabay.com/video/2020/01/15/31162-386264542_large.mp4",  # Dust and scratches
]

def main():
    print("🎬 Professional Vintage Video Workflow Test")
    print("=" * 70)
    
    # Step 1: Upload background image
    print("📸 Step 1: Uploading background image...")
    upload_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/storage",
        data={
            "url": "https://picsum.photos/1080/1920",
            "media_type": "image"
        }
    )
    
    if upload_response.status_code != 200:
        print(f"❌ Failed to upload image: {upload_response.text}")
        return
    
    background_id = upload_response.json()["file_id"]
    print(f"✅ Background image uploaded: {background_id}")
    
    # Step 2: Upload vintage texture overlay
    print("\n🎞️ Step 2: Uploading vintage texture overlay...")
    texture_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/storage",
        data={
            "url": VINTAGE_TEXTURE_URLS[0],  # Use first texture
            "media_type": "video"
        }
    )
    
    if texture_response.status_code != 200:
        print(f"❌ Failed to upload texture: {texture_response.text}")
        print("💡 You can manually upload a black-background dust/scratch video")
        texture_id = None
    else:
        texture_id = texture_response.json()["file_id"]
        print(f"✅ Vintage texture uploaded: {texture_id}")
    
    # Step 3: Generate enhanced karaoke video
    print("\n🎵 Step 3: Generating enhanced karaoke video...")
    karaoke_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
        data={
            "background_id": background_id,
            "text": TEST_TEXT,
            "caption_animation": "word",  # Enhanced karaoke
            "width": 1080,
            "height": 1920,
            # Enhanced readability settings
            "caption_config_shadow_transparency": 0.5,  # Darker shadow
            "caption_config_shadow_blur": 20,  # Larger, softer shadow
        }
    )
    
    if karaoke_response.status_code != 200:
        print(f"❌ Karaoke generation failed: {karaoke_response.text}")
        return
    
    karaoke_video_id = karaoke_response.json()["file_id"]
    print(f"✅ Enhanced karaoke video generation started: {karaoke_video_id}")
    
    # Step 4: Wait for video generation
    print("\n⏳ Step 4: Waiting for video generation...")
    time.sleep(15)  # Wait for generation to complete
    
    # Step 5: Apply vintage texture overlay (if texture was uploaded)
    final_video_id = karaoke_video_id  # Default to karaoke video
    
    if texture_id:
        print("\n🎨 Step 5: Applying vintage texture overlay...")
        overlay_response = requests.post(
            f"{API_BASE_URL}/api/v1/media/video-tools/add-colorkey-overlay",
            data={
                "video_id": karaoke_video_id,
                "overlay_video_id": texture_id,
                "color": "black",  # Make black background transparent
                "similarity": 0.15,  # Good for vintage textures
                "blend": 0.2,  # Smooth blending
            }
        )
        
        if overlay_response.status_code != 200:
            print(f"❌ Texture overlay failed: {overlay_response.text}")
            print("📝 Using karaoke video without texture overlay")
        else:
            final_video_id = overlay_response.json()["file_id"]
            print(f"✅ Vintage texture overlay applied: {final_video_id}")
    else:
        print("\n⚠️ Step 5: Skipping texture overlay (no texture uploaded)")
    
    # Step 6: Generate traditional video for comparison
    print("\n📝 Step 6: Generating traditional video for comparison...")
    traditional_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
        data={
            "background_id": background_id,
            "text": TEST_TEXT,
            "caption_animation": "segment",  # Traditional
            "width": 1080,
            "height": 1920,
        }
    )
    
    if traditional_response.status_code != 200:
        print(f"❌ Traditional generation failed: {traditional_response.text}")
        return
    
    traditional_video_id = traditional_response.json()["file_id"]
    print(f"✅ Traditional video generation started: {traditional_video_id}")
    
    # Results
    print("\n🎉 PROFESSIONAL VINTAGE WORKFLOW SUCCESS!")
    print("=" * 70)
    print(f"📋 Generated Videos:")
    print(f"   • Background Image: {background_id}")
    if texture_id:
        print(f"   • Vintage Texture: {texture_id}")
    print(f"   • Enhanced Karaoke Video: {karaoke_video_id}")
    print(f"   • Final Professional Video: {final_video_id}")
    print(f"   • Traditional Comparison: {traditional_video_id}")
    
    print(f"\n📥 Download Links:")
    print(f"   • Final Professional Video: {API_BASE_URL}/api/v1/media/storage/{final_video_id}")
    print(f"   • Traditional Comparison: {API_BASE_URL}/api/v1/media/storage/{traditional_video_id}")
    
    print(f"\n✨ Enhanced Features:")
    print(f"   • Smooth karaoke word-by-word animation")
    print(f"   • Enhanced text readability (70% → 100% opacity)")
    print(f"   • Larger, softer shadows for better contrast")
    print(f"   • Elegant EB Garamond italic typography")
    if texture_id:
        print(f"   • Authentic vintage film texture overlay")
        print(f"   • Professional dust and scratch effects")
    print(f"   • Broadcast-quality output")
    
    print(f"\n⏱️ Note: Allow time for background processing!")
    
    return {
        "background_id": background_id,
        "texture_id": texture_id,
        "karaoke_video_id": karaoke_video_id,
        "final_video_id": final_video_id,
        "traditional_video_id": traditional_video_id
    }

if __name__ == "__main__":
    try:
        results = main()
        if results:
            print(f"\n🔗 Quick Access:")
            print(f"Final video: {API_BASE_URL}/api/v1/media/storage/{results['final_video_id']}")
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure server is running at:", API_BASE_URL)
    except Exception as e:
        print(f"❌ Error: {e}")
