#!/usr/bin/env python3
"""
Test the complete professional video workflow:
1. Generate smooth karaoke video
2. Apply vintage film grain filter
3. Compare with traditional captions
"""
import requests
import time

# Configuration
API_BASE_URL = "http://152.53.86.6:8000"
TEST_TEXT = "This is a complete professional video workflow with smooth karaoke animation and vintage film grain."

def main():
    print("🎬 Complete Professional Video Workflow Test")
    print("=" * 70)
    
    # Step 1: Upload test image
    print("📸 Step 1: Uploading background image...")
    upload_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/storage",
        data={
            "url": "https://picsum.photos/1080/1920",
            "media_type": "image"
        }
    )
    
    if upload_response.status_code != 200:
        print(f"❌ Failed to upload image: {upload_response.text}")
        return
    
    background_id = upload_response.json()["file_id"]
    print(f"✅ Background image uploaded: {background_id}")
    
    # Step 2: Generate smooth karaoke video
    print("\n🎵 Step 2: Generating smooth karaoke video...")
    karaoke_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
        data={
            "background_id": background_id,
            "text": TEST_TEXT,
            "caption_animation": "word",  # Smooth karaoke
            "width": 1080,
            "height": 1920,
        }
    )
    
    if karaoke_response.status_code != 200:
        print(f"❌ Karaoke generation failed: {karaoke_response.text}")
        return
    
    karaoke_video_id = karaoke_response.json()["file_id"]
    print(f"✅ Karaoke video generation started: {karaoke_video_id}")
    
    # Step 3: Wait a bit for video generation (optional)
    print("\n⏳ Step 3: Waiting for video generation to complete...")
    print("   (In production, you'd poll for completion or use webhooks)")
    time.sleep(10)  # Wait 10 seconds
    
    # Step 4: Apply vintage filter
    print("\n🎞️  Step 4: Applying vintage film grain filter...")
    vintage_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/apply-vintage-filter",
        data={
            "video_id": karaoke_video_id,
            "grain_strength": 8,  # Subtle film grain
            "vignette_intensity": 0.1,  # Light vignette
        }
    )
    
    if vintage_response.status_code != 200:
        print(f"❌ Vintage filter failed: {vintage_response.text}")
        return
    
    final_video_id = vintage_response.json()["file_id"]
    print(f"✅ Vintage filter applied: {final_video_id}")
    
    # Step 5: Generate traditional video for comparison
    print("\n📝 Step 5: Generating traditional video for comparison...")
    traditional_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
        data={
            "background_id": background_id,
            "text": TEST_TEXT,
            "caption_animation": "segment",  # Traditional
            "width": 1080,
            "height": 1920,
        }
    )
    
    if traditional_response.status_code != 200:
        print(f"❌ Traditional generation failed: {traditional_response.text}")
        return
    
    traditional_video_id = traditional_response.json()["file_id"]
    print(f"✅ Traditional video generation started: {traditional_video_id}")
    
    # Results
    print("\n🎉 COMPLETE WORKFLOW SUCCESS!")
    print("=" * 70)
    print(f"📋 Generated Videos:")
    print(f"   • Background Image: {background_id}")
    print(f"   • Karaoke Video (raw): {karaoke_video_id}")
    print(f"   • Final Video (karaoke + vintage): {final_video_id}")
    print(f"   • Traditional Video: {traditional_video_id}")
    
    print(f"\n📥 Download Links:")
    print(f"   • Final Professional Video: {API_BASE_URL}/api/v1/media/storage/{final_video_id}")
    print(f"   • Traditional Comparison: {API_BASE_URL}/api/v1/media/storage/{traditional_video_id}")
    
    print(f"\n✨ Final Video Features:")
    print(f"   • Smooth karaoke word-by-word animation")
    print(f"   • Semi-transparent to opaque color fill")
    print(f"   • Elegant EB Garamond italic typography")
    print(f"   • Soft shadow with no harsh outlines")
    print(f"   • Vintage film grain texture")
    print(f"   • Subtle vignette effect")
    print(f"   • Professional broadcast quality")
    
    print(f"\n⏱️  Note: Allow time for background processing before downloading!")
    
    return {
        "background_id": background_id,
        "karaoke_video_id": karaoke_video_id,
        "final_video_id": final_video_id,
        "traditional_video_id": traditional_video_id
    }

if __name__ == "__main__":
    try:
        results = main()
        if results:
            print(f"\n🔗 Quick Access:")
            print(f"Final video: {API_BASE_URL}/api/v1/media/storage/{results['final_video_id']}")
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure server is running at:", API_BASE_URL)
    except Exception as e:
        print(f"❌ Error: {e}")
