#!/usr/bin/env python3
"""
Simple caption test
"""
import requests
from PIL import Image
import tempfile
import os

API_BASE_URL = "http://152.53.86.6:8000"
TEST_TEXT = "Hello world test"

def test_captions():
    print("Testing captions with a colorful background...")

    # Create a colorful background instead of black
    img = Image.new('RGB', (1080, 1920))
    pixels = img.load()

    # Create a gradient background (blue to orange like your example)
    for y in range(1920):
        for x in range(1080):
            # Create gradient
            r = int(255 * (y / 1920))  # Red increases with y
            g = int(100 + 155 * (x / 1080))  # Green varies with x
            b = int(255 * (1 - y / 1920))  # Blue decreases with y
            pixels[x, y] = (r, g, b)

    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
        img.save(temp_file.name, 'PNG')
        temp_image_path = temp_file.name

    try:
        print(f"Created test image: {temp_image_path}")

        # Upload
        with open(temp_image_path, 'rb') as f:
            upload_response = requests.post(
                f"{API_BASE_URL}/api/v1/media/storage",
                files={"file": ("bg.png", f, "image/png")},
                data={"media_type": "image"}
            )
        
        if upload_response.status_code != 200:
            print(f"Upload failed: {upload_response.text}")
            return

        background_id = upload_response.json()["file_id"]
        print(f"Background uploaded: {background_id}")

        # Generate video with captions - using maximum visibility settings
        print("Generating video with high-contrast captions...")
        video_response = requests.post(
            f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
            data={
                "background_id": background_id,
                "text": TEST_TEXT,
                "caption_animation": "word",  # Karaoke style
                "width": 1080,
                "height": 1920,
                # Maximum visibility settings
                "caption_config_font_color": "#FFFFFF",      # Pure white
                "caption_config_stroke_color": "#000000",    # Black outline
                "caption_config_stroke_size": 6,             # Thick outline
                "caption_config_font_size": 120,             # Large font
                "caption_config_font_bold": True,            # Bold
                "caption_config_subtitle_position": "center", # Center
                "caption_config_shadow_color": "#000000",    # Black shadow
                "caption_config_shadow_transparency": 0.0,   # No transparency
                "caption_config_shadow_blur": 0,             # No blur for crisp edges
            }
        )
        
        if video_response.status_code != 200:
            print(f"Video generation failed: {video_response.text}")
            return

        video_id = video_response.json()["file_id"]
        print(f"✅ Video generated successfully: {video_id}")
        print(f"🔗 Download URL: {API_BASE_URL}/api/v1/media/storage/{video_id}")
        print(f"📝 Text: '{TEST_TEXT}'")
        print(f"🎨 Style: White text with thick black outline, karaoke animation")

        # Also test traditional style for comparison
        print("\n📋 Testing traditional style for comparison...")
        traditional_response = requests.post(
            f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
            data={
                "background_id": background_id,
                "text": TEST_TEXT,
                "caption_animation": "segment",  # Traditional style
                "width": 1080,
                "height": 1920,
                # Same styling
                "caption_config_font_color": "#FFFFFF",
                "caption_config_stroke_color": "#000000",
                "caption_config_stroke_size": 6,
                "caption_config_font_size": 120,
                "caption_config_font_bold": True,
                "caption_config_subtitle_position": "center",
            }
        )

        if traditional_response.status_code == 200:
            traditional_id = traditional_response.json()["file_id"]
            print(f"✅ Traditional video: {traditional_id}")
            print(f"🔗 Traditional URL: {API_BASE_URL}/api/v1/media/storage/{traditional_id}")
        else:
            print(f"❌ Traditional video failed: {traditional_response.text}")
        
    finally:
        if os.path.exists(temp_image_path):
            os.unlink(temp_image_path)

if __name__ == "__main__":
    test_captions()
