#!/usr/bin/env python3
"""
Test the smooth karaoke color-fill animation
"""
import requests

# Configuration
API_BASE_URL = "http://152.53.86.6:8000"
TEST_TEXT = "Hello world, this is a beautiful smooth karaoke animation test."

def main():
    print("🎨 Testing Smooth Karaoke Color-Fill Animation")
    print("=" * 60)
    
    # Step 1: Upload test image
    print("📸 Uploading test image...")
    upload_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/storage",
        data={
            "url": "https://picsum.photos/1080/1920",
            "media_type": "image"
        }
    )
    
    if upload_response.status_code != 200:
        print(f"❌ Failed to upload image: {upload_response.text}")
        return
    
    background_id = upload_response.json()["file_id"]
    print(f"✅ Image uploaded: {background_id}")
    
    # Step 2: Test smooth karaoke animation
    print("\n🎵 Testing smooth karaoke animation...")
    karaoke_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
        data={
            "background_id": background_id,
            "text": TEST_TEXT,
            "caption_animation": "word",  # Karaoke style
            "width": 1080,
            "height": 1920,
            # Using the new beautiful defaults:
            # - EB Garamond italic font
            # - Center position
            # - Soft shadow with blur
            # - No stroke outline
        }
    )
    
    if karaoke_response.status_code == 200:
        karaoke_id = karaoke_response.json()["file_id"]
        print(f"✅ Smooth karaoke video started! ID: {karaoke_id}")
    else:
        print(f"❌ Karaoke test failed: {karaoke_response.text}")
        return
    
    # Step 3: Test traditional for comparison
    print("\n📝 Testing traditional captions...")
    traditional_response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
        data={
            "background_id": background_id,
            "text": TEST_TEXT,
            "caption_animation": "segment",  # Traditional
            "width": 1080,
            "height": 1920,
        }
    )
    
    if traditional_response.status_code == 200:
        traditional_id = traditional_response.json()["file_id"]
        print(f"✅ Traditional video started! ID: {traditional_id}")
    else:
        print(f"❌ Traditional test failed: {traditional_response.text}")
        return
    
    # Results
    print("\n🎉 SUCCESS! Smooth karaoke animation test completed!")
    print("=" * 60)
    print(f"📋 Results:")
    print(f"   • Background Image: {background_id}")
    print(f"   • Smooth Karaoke Video: {karaoke_id}")
    print(f"   • Traditional Video: {traditional_id}")
    print(f"\n📥 Download:")
    print(f"   • Karaoke: {API_BASE_URL}/api/v1/media/storage/{karaoke_id}")
    print(f"   • Traditional: {API_BASE_URL}/api/v1/media/storage/{traditional_id}")
    print(f"\n✨ What to expect in the karaoke video:")
    print(f"   • Words start semi-transparent (50% opacity)")
    print(f"   • Smooth color fill as each word is spoken")
    print(f"   • Elegant EB Garamond italic font")
    print(f"   • Soft shadow with no harsh outline")
    print(f"   • Professional, polished appearance")
    print(f"\n⏱️  Videos generate in background - wait before downloading!")

if __name__ == "__main__":
    try:
        main()
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed! Make sure server is running at:", API_BASE_URL)
    except Exception as e:
        print(f"❌ Error: {e}")
