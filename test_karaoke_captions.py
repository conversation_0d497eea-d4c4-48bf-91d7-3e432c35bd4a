#!/usr/bin/env python3
"""
Test script for karaoke-style caption animation
"""
import requests
import json
import time

# Configuration
API_BASE_URL = "http://***********:8000"  # Adjust to your server URL
TEST_IMAGE_URL = "https://upload.wikimedia.org/wikipedia/commons/thumb/b/bd/Test.svg/1280px-Test.svg.png"  # Free test image service
TEST_TEXT = "Hello world, this is a test of karaoke style captions."

def upload_test_image():
    """Upload a test image and return its ID"""
    print("Uploading test image...")

    # Upload from URL
    response = requests.post(
        f"{API_BASE_URL}/api/v1/media/storage",
        data={
            "url": TEST_IMAGE_URL,
            "media_type": "image"
        }
    )

    if response.status_code == 200:
        result = response.json()
        image_id = result.get("file_id")
        print(f"✅ Image uploaded successfully with ID: {image_id}")
        return image_id
    else:
        print(f"❌ Failed to upload image: {response.status_code}")
        print(response.text)
        return None

def test_karaoke_captions(background_id):
    """Test the new karaoke-style caption animation"""

    # Test data
    test_data = {
        "background_id": background_id,
        "text": TEST_TEXT,
        "width": 1080,
        "height": 1920,
        "caption_animation": "word",  # This is the new parameter!
        "caption_config_line_max_length": 25,
        "caption_config_font_size": 120,
        "caption_config_font_name": "EB Garamond",
        "caption_config_subtitle_position": "bottom"
    }
    
    print("Testing karaoke-style captions...")
    print(f"Text: {TEST_TEXT}")
    print(f"Animation style: {test_data['caption_animation']}")
    
    # Make API request
    response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
        data=test_data
    )
    
    if response.status_code == 200:
        result = response.json()
        file_id = result.get("file_id")
        print(f"✅ Success! Video generation started with ID: {file_id}")
        print("Check the generated video to see karaoke-style word highlighting")
        return file_id
    else:
        print(f"❌ Error: {response.status_code}")
        print(response.text)
        return None

def test_traditional_captions(background_id):
    """Test traditional segment-based captions for comparison"""

    test_data = {
        "background_id": background_id,
        "text": TEST_TEXT,
        "width": 1080,
        "height": 1920,
        "caption_animation": "segment",  # Traditional style
        "caption_config_line_max_length": 25,
        "caption_config_font_size": 120,
        "caption_config_font_name": "EB Garamond",
        "caption_config_subtitle_position": "bottom"
    }
    
    print("\nTesting traditional segment captions for comparison...")
    
    response = requests.post(
        f"{API_BASE_URL}/api/v1/media/video-tools/generate/tts-captioned-video",
        data=test_data
    )
    
    if response.status_code == 200:
        result = response.json()
        file_id = result.get("file_id")
        print(f"✅ Success! Traditional video generation started with ID: {file_id}")
        return file_id
    else:
        print(f"❌ Error: {response.status_code}")
        print(response.text)
        return None

if __name__ == "__main__":
    print("=== Karaoke Caption Animation Test ===")
    print("This script will:")
    print("1. Upload a test image automatically")
    print("2. Test karaoke-style captions")
    print("3. Test traditional captions for comparison")
    print("4. Make sure your API server is running\n")

    # Upload test image first
    background_id = upload_test_image()
    if not background_id:
        print("❌ Failed to upload test image. Exiting.")
        exit(1)

    # Test both animation styles
    karaoke_id = test_karaoke_captions(background_id)
    traditional_id = test_traditional_captions(background_id)

    if karaoke_id and traditional_id:
        print(f"\n=== Results ===")
        print(f"Karaoke video ID: {karaoke_id}")
        print(f"Traditional video ID: {traditional_id}")
        print(f"Background image ID: {background_id}")
        print("\nCompare both videos to see the difference!")
        print(f"Download videos at: {API_BASE_URL}/api/v1/media/storage/{{video_id}}")
    else:
        print("\n❌ Some tests failed. Check the server logs for details.")
